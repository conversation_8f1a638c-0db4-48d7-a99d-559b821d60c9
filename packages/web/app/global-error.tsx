'use client';

// Force dynamic rendering to avoid SSR issues
export const dynamic = 'force-dynamic';

import { But<PERSON>, Container, Flex, Heading, Text } from '@radix-ui/themes';
import { useEffect } from 'react';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Global error:', error);
  }, [error]);

  return (
    <html lang="en">
      <body>
        <Container size="2" className="flex min-h-screen items-center justify-center">
          <Flex direction="column" align="center" gap="6" className="text-center">
            <Heading size="8" className="font-bold text-5xl text-red-600">
              Something went wrong!
            </Heading>
            <Text size="4" color="gray" className="max-w-md">
              We're sorry, but something unexpected happened. Please try refreshing the page or go
              back to the homepage.
            </Text>
            {error.digest && (
              <Text size="2" color="gray" className="font-mono">
                Error ID: {error.digest}
              </Text>
            )}
            <Flex gap="3" className="mt-4">
              <Button variant="solid" onClick={reset}>
                Try again
              </Button>
              <a href="/">
                <Button variant="outline">Go Home</Button>
              </a>
            </Flex>
          </Flex>
        </Container>
      </body>
    </html>
  );
}
